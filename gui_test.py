#!/usr/bin/env python3
"""
Test the GUI components to ensure they're visible
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gui_visibility():
    """Test GUI component visibility"""
    print("Testing GUI visibility...")
    
    try:
        from main import TrafficAnalysisApp
        
        # Create app instance
        app = TrafficAnalysisApp()
        
        # Add a test message to verify visibility
        test_label = tk.Label(app.root, 
                             text="🎉 GUI IS WORKING! You should see controls and panels below.",
                             font=('Arial', 14, 'bold'),
                             bg='lightgreen',
                             fg='black',
                             pady=10)
        test_label.pack(side=tk.TOP, fill=tk.X)
        
        # Force update to ensure everything is drawn
        app.root.update()
        
        print("✅ GUI created successfully!")
        print("🎯 Look for:")
        print("  - Video Display area (dark gray with text)")
        print("  - Control buttons (Upload Video, Start Demo, etc.)")
        print("  - Statistics panel on the right")
        print("  - Speed legend")
        print("  - Speed chart area")
        print("\n🚀 Try clicking 'Start Demo' to see the simulation!")
        
        # Run the application
        app.run()
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui_visibility()
