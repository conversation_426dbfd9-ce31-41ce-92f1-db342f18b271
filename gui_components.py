import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from PIL import Image, ImageTk
import cv2

class SpeedChart:
    def __init__(self, parent):
        self.parent = parent
        self.setup_chart()
        
    def setup_chart(self):
        """Setup the speed chart widget"""
        self.chart_frame = ttk.LabelFrame(self.parent, text="Speed Analysis", padding=10)
        
        # Create matplotlib figure
        self.fig, self.ax = plt.subplots(figsize=(6, 4))
        self.canvas = FigureCanvasTkAgg(self.fig, self.chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Initialize data
        self.speed_data = []
        self.time_data = []
        
        # Setup plot
        self.ax.set_xlabel('Time (s)')
        self.ax.set_ylabel('Speed (km/h)')
        self.ax.set_title('Real-time Speed Tracking')
        self.ax.grid(True, alpha=0.3)
        
        # Speed threshold lines
        self.threshold_lines = {}
        
    def update_chart(self, speed_data, time_data, thresholds):
        """Update the speed chart with new data"""
        self.speed_data = speed_data
        self.time_data = time_data
        
        # Clear previous plot
        self.ax.clear()
        
        if len(speed_data) > 0:
            # Plot speed data
            self.ax.plot(time_data, speed_data, 'b-', linewidth=2, label='Speed')
            
            # Add threshold lines
            colors = ['blue', 'green', 'orange', 'red']
            threshold_names = ['slow', 'normal', 'fast', 'speeding']
            
            for i, (name, threshold) in enumerate(thresholds.items()):
                if threshold > 0:
                    self.ax.axhline(y=threshold, color=colors[i], linestyle='--', 
                                  alpha=0.7, label=f'{name.title()} ({threshold} km/h)')
        
        # Setup plot
        self.ax.set_xlabel('Time (s)')
        self.ax.set_ylabel('Speed (km/h)')
        self.ax.set_title('Real-time Speed Tracking')
        self.ax.grid(True, alpha=0.3)

        # Only show legend if there are labeled artists
        handles, labels = self.ax.get_legend_handles_labels()
        if handles:
            self.ax.legend()
        
        # Update canvas
        self.canvas.draw()
    
    def pack(self, **kwargs):
        """Pack the chart widget"""
        self.chart_frame.pack(**kwargs)

class StatisticsPanel:
    def __init__(self, parent):
        self.parent = parent
        self.setup_panel()
        
    def setup_panel(self):
        """Setup the statistics panel"""
        self.panel_frame = ttk.LabelFrame(self.parent, text="Live Statistics", padding=10)
        
        # Statistics variables
        self.stats_vars = {}
        
        # Create statistics labels
        stats_items = [
            ("Total Vehicles", "total_vehicles"),
            ("Current Vehicles", "current_vehicles"),
            ("Average Speed", "avg_speed"),
            ("Maximum Speed", "max_speed"),
            ("Speed Violations", "violations"),
            ("Detection Rate", "detection_rate")
        ]
        
        for label, key in stats_items:
            frame = ttk.Frame(self.panel_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(frame, text=f"{label}:").pack(side=tk.LEFT)
            
            var = tk.StringVar(value="0")
            self.stats_vars[key] = var
            
            label_widget = ttk.Label(frame, textvariable=var, 
                                   font=('Arial', 10, 'bold'))
            label_widget.pack(side=tk.RIGHT)
    
    def update_statistics(self, stats):
        """Update statistics display"""
        for key, value in stats.items():
            if key in self.stats_vars:
                if isinstance(value, float):
                    self.stats_vars[key].set(f"{value:.1f}")
                else:
                    self.stats_vars[key].set(str(value))
    
    def pack(self, **kwargs):
        """Pack the panel widget"""
        self.panel_frame.pack(**kwargs)

class SpeedLegend:
    def __init__(self, parent):
        self.parent = parent
        self.setup_legend()
        
    def setup_legend(self):
        """Setup the speed legend"""
        self.legend_frame = ttk.LabelFrame(self.parent, text="Speed Legend", padding=10)
        
        # Legend items
        legend_items = [
            ("Slow (< 30 km/h)", "#3498db"),
            ("Normal (30-50 km/h)", "#2ecc71"),
            ("Fast (50-70 km/h)", "#f39c12"),
            ("Speeding (> 70 km/h)", "#e74c3c")
        ]
        
        for label, color in legend_items:
            frame = ttk.Frame(self.legend_frame)
            frame.pack(fill=tk.X, pady=2)
            
            # Color indicator
            canvas = tk.Canvas(frame, width=20, height=15, bg=color, 
                             highlightthickness=0)
            canvas.pack(side=tk.LEFT, padx=(0, 5))
            
            # Label
            ttk.Label(frame, text=label).pack(side=tk.LEFT)
    
    def pack(self, **kwargs):
        """Pack the legend widget"""
        self.legend_frame.pack(**kwargs)

class ControlPanel:
    def __init__(self, parent, callbacks):
        self.parent = parent
        self.callbacks = callbacks
        self.setup_panel()
        
    def setup_panel(self):
        """Setup the control panel"""
        self.control_frame = ttk.LabelFrame(self.parent, text="Controls", padding=10)
        
        # Button frame
        button_frame = ttk.Frame(self.control_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        # Control buttons with better styling
        ttk.Button(button_frame, text="📁 Upload Video",
                  command=self.callbacks.get('upload', lambda: None)).pack(side=tk.LEFT, padx=5, pady=2)
        ttk.Button(button_frame, text="🚗 Start Demo",
                  command=self.callbacks.get('demo', lambda: None)).pack(side=tk.LEFT, padx=5, pady=2)
        ttk.Button(button_frame, text="⏯️ Play/Pause",
                  command=self.callbacks.get('play_pause', lambda: None)).pack(side=tk.LEFT, padx=5, pady=2)
        ttk.Button(button_frame, text="⏹️ Stop",
                  command=self.callbacks.get('stop', lambda: None)).pack(side=tk.LEFT, padx=5, pady=2)
        
        # Settings frame
        settings_frame = ttk.Frame(self.control_frame)
        settings_frame.pack(fill=tk.X, pady=5)
        
        # Speed threshold controls
        ttk.Label(settings_frame, text="Speed Thresholds (km/h):").pack(anchor=tk.W)
        
        self.threshold_vars = {}
        thresholds = [
            ("Slow", "slow", 30),
            ("Normal", "normal", 50),
            ("Fast", "fast", 70),
            ("Speeding", "speeding", 80)
        ]
        
        for label, key, default in thresholds:
            frame = ttk.Frame(settings_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(frame, text=f"{label}:").pack(side=tk.LEFT)
            
            var = tk.StringVar(value=str(default))
            self.threshold_vars[key] = var
            
            entry = ttk.Entry(frame, textvariable=var, width=10)
            entry.pack(side=tk.RIGHT)
        
        # Export button
        ttk.Button(self.control_frame, text="📊 Export Statistics",
                  command=self.callbacks.get('export', lambda: None)).pack(fill=tk.X, pady=10)
    
    def get_thresholds(self):
        """Get current threshold values"""
        thresholds = {}
        for key, var in self.threshold_vars.items():
            try:
                thresholds[key] = float(var.get())
            except ValueError:
                thresholds[key] = 0
        return thresholds
    
    def pack(self, **kwargs):
        """Pack the control panel"""
        self.control_frame.pack(**kwargs)

class VideoDisplay:
    def __init__(self, parent):
        self.parent = parent
        self.setup_display()
        
    def setup_display(self):
        """Setup the video display"""
        self.display_frame = ttk.LabelFrame(self.parent, text="Video Display", padding=10)

        # Video label with better visibility
        self.video_label = tk.Label(self.display_frame,
                                   bg='#2c3e50',
                                   fg='white',
                                   text="Click 'Start Demo' or 'Upload Video' to begin",
                                   font=('Arial', 12),
                                   width=100,
                                   height=30,
                                   relief='sunken',
                                   borderwidth=2)
        self.video_label.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

        # Status label
        self.status_label = ttk.Label(self.display_frame, text="System Ready - Upload a video or start demo",
                                     font=('Arial', 10, 'bold'))
        self.status_label.pack(pady=5)
    
    def update_frame(self, frame):
        """Update the video display with a new frame"""
        if frame is None:
            return
        
        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Resize frame to fit display
        height, width = frame_rgb.shape[:2]
        max_width, max_height = 800, 600
        
        if width > max_width or height > max_height:
            scale = min(max_width/width, max_height/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            frame_rgb = cv2.resize(frame_rgb, (new_width, new_height))
        
        # Convert to PIL Image
        pil_image = Image.fromarray(frame_rgb)
        photo = ImageTk.PhotoImage(pil_image)
        
        # Update label
        self.video_label.configure(image=photo)
        self.video_label.image = photo  # Keep a reference
    
    def update_status(self, status):
        """Update status display"""
        self.status_label.config(text=status)
    
    def pack(self, **kwargs):
        """Pack the display widget"""
        self.display_frame.pack(**kwargs)

class AlertDialog:
    def __init__(self, parent, title, message):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x200")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # Message
        ttk.Label(self.dialog, text=message, wraplength=350).pack(pady=20)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="OK", 
                  command=self.dialog.destroy).pack(side=tk.LEFT, padx=10)
        
        # Make dialog modal
        self.dialog.wait_window() 