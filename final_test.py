#!/usr/bin/env python3
"""
Final comprehensive test of the Traffic Analysis System
"""

import sys
import os
import time
import subprocess

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dependencies():
    """Test all required dependencies"""
    print("🔍 Testing dependencies...")
    
    required_modules = [
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('PIL', 'Pillow'),
        ('matplotlib', 'Matplotlib'),
        ('pandas', 'Pandas'),
        ('sklearn', 'Scikit-learn'),
        ('imutils', 'Imutils')
    ]
    
    failed = []
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError:
            print(f"  ❌ {name}")
            failed.append(module)
    
    if failed:
        print(f"\n❌ Missing dependencies: {', '.join(failed)}")
        return False
    
    print("✅ All dependencies available")
    return True

def test_custom_modules():
    """Test custom application modules"""
    print("\n🔍 Testing custom modules...")
    
    modules = [
        ('vehicle_detector', 'Vehicle Detector'),
        ('speed_calculator', 'Speed Calculator'),
        ('gui_components', 'GUI Components'),
        ('error_handler', 'Error Handler')
    ]
    
    failed = []
    for module, name in modules:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError as e:
            print(f"  ❌ {name}: {e}")
            failed.append(module)
    
    if failed:
        print(f"\n❌ Failed modules: {', '.join(failed)}")
        return False
    
    print("✅ All custom modules working")
    return True

def test_main_app_import():
    """Test main application import"""
    print("\n🔍 Testing main application import...")
    
    try:
        from main import TrafficAnalysisApp
        print("  ✅ Main application imports successfully")
        return True
    except Exception as e:
        print(f"  ❌ Main application import failed: {e}")
        return False

def test_core_functionality():
    """Test core functionality without GUI"""
    print("\n🔍 Testing core functionality...")
    
    try:
        from vehicle_detector import VehicleDetector
        from speed_calculator import SpeedCalculator
        import numpy as np
        
        # Test vehicle detector
        detector = VehicleDetector()
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        vehicles, mask = detector.detect_vehicles(test_frame)
        
        # Test speed calculator
        calculator = SpeedCalculator()
        speed = calculator.calculate_speed(1, (100, 100), time.time())
        
        # Test thresholds
        thresholds = {'slow': 30, 'normal': 50, 'fast': 70}
        color = detector.get_speed_color(45, thresholds)
        
        print("  ✅ Core functionality working")
        return True
    except Exception as e:
        print(f"  ❌ Core functionality failed: {e}")
        return False

def test_gui_creation():
    """Test GUI component creation"""
    print("\n🔍 Testing GUI creation...")
    
    try:
        import tkinter as tk
        from gui_components import VideoDisplay, ControlPanel, StatisticsPanel, SpeedLegend
        
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Create components
        video_display = VideoDisplay(root)
        control_panel = ControlPanel(root, {})
        stats_panel = StatisticsPanel(root)
        speed_legend = SpeedLegend(root)
        
        # Test threshold getting
        thresholds = control_panel.get_thresholds()
        
        root.destroy()
        print("  ✅ GUI components working")
        return True
    except Exception as e:
        print(f"  ❌ GUI creation failed: {e}")
        return False

def test_application_startup():
    """Test application startup (without mainloop)"""
    print("\n🔍 Testing application startup...")
    
    try:
        from main import TrafficAnalysisApp
        
        # Create app instance
        app = TrafficAnalysisApp()
        
        # Test demo vehicle setup
        app.setup_demo_vehicles()
        
        # Test statistics reset
        app.reset_statistics()
        
        # Clean up
        app.root.destroy()
        
        print("  ✅ Application startup working")
        return True
    except Exception as e:
        print(f"  ❌ Application startup failed: {e}")
        return False

def test_batch_file():
    """Test batch file exists and is properly formatted"""
    print("\n🔍 Testing batch file...")
    
    try:
        if os.path.exists("start_traffic_analysis.bat"):
            with open("start_traffic_analysis.bat", 'r') as f:
                content = f.read()
                if "python run.py" in content:
                    print("  ✅ Batch file properly configured")
                    return True
                else:
                    print("  ⚠️  Batch file exists but may not be configured correctly")
                    return True
        else:
            print("  ⚠️  Batch file not found (this is okay)")
            return True
    except Exception as e:
        print(f"  ❌ Batch file test failed: {e}")
        return False

def main():
    """Run all final tests"""
    print("🚗 Traffic Video Analysis System - Final Test")
    print("=" * 60)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Custom Modules", test_custom_modules),
        ("Main App Import", test_main_app_import),
        ("Core Functionality", test_core_functionality),
        ("GUI Creation", test_gui_creation),
        ("Application Startup", test_application_startup),
        ("Batch File", test_batch_file)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Final Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is fully functional!")
        print("\n📋 How to run the application:")
        print("  1. python run.py (recommended)")
        print("  2. python main.py")
        print("  3. .\\start_traffic_analysis.bat (Windows)")
        print("\n🚀 The system is ready for use!")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
