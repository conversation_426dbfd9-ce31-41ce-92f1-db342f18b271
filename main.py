#!/usr/bin/env python3
"""
Traffic Video Analysis System
A Python-based application for real-time vehicle detection and speed tracking.

Author: Traffic Analysis Team
Version: 1.0
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from datetime import datetime
import cv2
import numpy as np
import random
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
from collections import deque

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from vehicle_detector import VehicleDetector
    from speed_calculator import SpeedCalculator
    from gui_components import (
        SpeedChart, StatisticsPanel, SpeedLegend, 
        ControlPanel, VideoDisplay, AlertDialog
    )
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure all required packages are installed:")
    print("pip install -r requirements.txt")
    sys.exit(1)

class TrafficAnalysisApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_main_window()
        self.setup_components()
        self.setup_callbacks()
        self.initialize_system()
        
    def setup_main_window(self):
        """Setup the main application window"""
        self.root.title("Traffic Video Analysis System v1.0")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # Set window icon if available
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # Configure style with better visibility
        style = ttk.Style()
        style.theme_use('default')

        # Configure colors for better visibility
        style.configure('TLabel', background='#f0f0f0', foreground='black')
        style.configure('TFrame', background='#f0f0f0')
        style.configure('TLabelframe', background='#e0e0e0', relief='solid', borderwidth=1)
        style.configure('TLabelframe.Label', background='#e0e0e0', foreground='black', font=('Arial', 9, 'bold'))
        style.configure('TButton', padding=5)
        
    def setup_components(self):
        """Setup all GUI components"""
        # Main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for video and controls
        left_panel = ttk.Frame(main_container)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Video display
        self.video_display = VideoDisplay(left_panel)
        self.video_display.pack(fill=tk.BOTH, expand=True)
        
        # Control panel
        self.control_panel = ControlPanel(left_panel, {})
        self.control_panel.pack(fill=tk.X, pady=5)
        
        # Right panel for statistics and charts
        right_panel = ttk.Frame(main_container)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        # Statistics panel
        self.stats_panel = StatisticsPanel(right_panel)
        self.stats_panel.pack(fill=tk.X, pady=5)
        
        # Speed legend
        self.speed_legend = SpeedLegend(right_panel)
        self.speed_legend.pack(fill=tk.X, pady=5)
        
        # Speed chart
        self.speed_chart = SpeedChart(right_panel)
        self.speed_chart.pack(fill=tk.BOTH, expand=True, pady=5)
        
    def setup_callbacks(self):
        """Setup callback functions for GUI components"""
        callbacks = {
            'upload': self.upload_video,
            'demo': self.start_demo,
            'play_pause': self.toggle_play,
            'stop': self.stop_analysis,
            'export': self.export_statistics
        }
        
        # Update control panel callbacks
        self.control_panel.callbacks = callbacks
        
    def initialize_system(self):
        """Initialize the analysis system"""
        try:
            # Initialize components
            self.vehicle_detector = VehicleDetector()
            self.speed_calculator = SpeedCalculator()
            
            # Initialize state variables
            self.is_playing = False
            self.is_demo_mode = False
            self.video_path = None
            self.cap = None
            self.current_frame = None
            self.frame_count = 0
            self.fps = 30
            
            # Demo vehicles
            self.demo_vehicles = []
            self.setup_demo_vehicles()
            
            # Statistics
            self.statistics = {
                'total_vehicles': 0,
                'current_vehicles': 0,
                'avg_speed': 0,
                'max_speed': 0,
                'violations': 0,
                'detection_rate': 0
            }
            
            # Speed tracking data
            self.speed_data = []
            self.time_data = []
            self.start_time = time.time()
            
            # Update status
            self.video_display.update_status("System Ready - Upload a video or start demo")
            
        except Exception as e:
            self.show_error("Initialization Error", f"Failed to initialize system: {str(e)}")
    
    def setup_demo_vehicles(self):
        """Initialize demo vehicles with realistic behavior"""
        vehicle_types = ["car", "truck", "bus", "motorcycle"]
        
        for i in range(6):
            vehicle_type = random.choice(vehicle_types)
            x = random.randint(50, 750)
            y = random.randint(100, 500)
            width = random.randint(30, 80)
            height = random.randint(20, 40)
            
            self.demo_vehicles.append({
                'type': vehicle_type,
                'x': x, 'y': y, 'width': width, 'height': height,
                'speed': random.uniform(30, 60),
                'direction': random.choice([-1, 1]),
                'lane': random.randint(0, 2),
                'id': random.randint(1000, 9999)
            })
    
    def upload_video(self):
        """Upload and load a video file"""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.video_path = file_path
                self.load_video()
                self.video_display.update_status(f"Loaded: {os.path.basename(file_path)}")
            except Exception as e:
                self.show_error("Video Load Error", f"Could not load video: {str(e)}")
    
    def load_video(self):
        """Load the selected video file"""
        if self.cap:
            self.cap.release()
        
        self.cap = cv2.VideoCapture(self.video_path)
        if not self.cap.isOpened():
            raise Exception("Could not open video file")
        
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.frame_count = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.is_demo_mode = False
        self.is_playing = False
        
        # Reset statistics
        self.reset_statistics()
        
        # Start video processing
        self.process_video()
    
    def start_demo(self):
        """Start demo mode with simulated vehicles"""
        self.is_demo_mode = True
        self.is_playing = True
        self.reset_statistics()
        
        # Start demo processing in a separate thread
        demo_thread = threading.Thread(target=self.process_demo, daemon=True)
        demo_thread.start()
        
        self.video_display.update_status("Demo Mode Active - Simulating traffic")
    
    def process_demo(self):
        """Process demo mode with simulated vehicle movement"""
        frame_count = 0
        
        while self.is_playing and self.is_demo_mode:
            # Create demo frame
            frame = np.zeros((600, 800, 3), dtype=np.uint8)
            frame[:] = (50, 50, 50)  # Dark gray background
            
            # Draw road
            self.draw_road_lanes(frame)
            
            # Update and draw vehicles
            current_vehicles = 0
            speeds = []
            
            for vehicle in self.demo_vehicles:
                # Update vehicle position
                self.update_demo_vehicle(vehicle)
                
                # Draw vehicle
                self.draw_demo_vehicle(frame, vehicle)
                
                # Draw speed bar
                self.draw_demo_speed_bar(frame, vehicle)
                
                current_vehicles += 1
                speeds.append(vehicle['speed'])
            
            # Update statistics
            self.update_demo_statistics(current_vehicles, speeds)
            
            # Update display
            self.video_display.update_frame(frame)
            
            # Update speed chart
            self.update_speed_chart()
            
            frame_count += 1
            time.sleep(0.1)  # 10 FPS for demo
    
    def draw_road_lanes(self, frame):
        """Draw road lanes on demo frame"""
        height, width = frame.shape[:2]
        
        # Draw lane dividers
        for i in range(1, 4):
            y = height // 4 * i
            cv2.line(frame, (0, y), (width, y), (255, 255, 255), 2)
    
    def update_demo_vehicle(self, vehicle):
        """Update demo vehicle position"""
        # Vehicle-specific behavior
        if vehicle['type'] == "car":
            speed_range = (30, 60)
            agility = 0.8
        elif vehicle['type'] == "truck":
            speed_range = (25, 45)
            agility = 0.4
        elif vehicle['type'] == "bus":
            speed_range = (22, 38)
            agility = 0.3
        else:  # motorcycle
            speed_range = (30, 70)
            agility = 1.0
        
        # Update speed with some randomness
        target_speed = random.uniform(*speed_range)
        vehicle['speed'] = vehicle['speed'] * 0.9 + target_speed * 0.1
        
        # Update position
        dx = vehicle['speed'] * vehicle['direction'] * 0.1 * agility
        vehicle['x'] += dx
        
        # Lane changing behavior
        if random.random() < 0.01:  # 1% chance to change lane
            vehicle['lane'] = random.randint(0, 2)
        
        # Update y position based on lane
        target_y = 150 + vehicle['lane'] * 150
        vehicle['y'] = vehicle['y'] * 0.95 + target_y * 0.05
        
        # Wrap around screen
        if vehicle['x'] > 850:
            vehicle['x'] = -50
        elif vehicle['x'] < -50:
            vehicle['x'] = 850
    
    def draw_demo_vehicle(self, frame, vehicle):
        """Draw demo vehicle on frame"""
        x, y = int(vehicle['x']), int(vehicle['y'])
        w, h = vehicle['width'], vehicle['height']
        
        # Vehicle colors
        colors = {
            "car": (0, 255, 0),      # Green
            "truck": (255, 0, 0),    # Red
            "bus": (0, 0, 255),      # Blue
            "motorcycle": (255, 255, 0)  # Yellow
        }
        
        color = colors.get(vehicle['type'], (0, 255, 0))
        
        # Draw vehicle rectangle
        cv2.rectangle(frame, (x-w//2, y-h//2), (x+w//2, y+h//2), color, -1)
        cv2.rectangle(frame, (x-w//2, y-h//2), (x+w//2, y+h//2), (255, 255, 255), 2)
        
        # Draw vehicle type label
        cv2.putText(frame, vehicle['type'].upper(), 
                   (x-w//2, y-h//2-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Draw vehicle ID
        cv2.putText(frame, f"#{vehicle['id']}", 
                   (x-w//2, y+h//2+15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    def draw_demo_speed_bar(self, frame, vehicle):
        """Draw speed bar on demo vehicle"""
        x, y = int(vehicle['x']), int(vehicle['y'])
        w, h = vehicle['width'], vehicle['height']
        
        # Calculate bar width based on speed
        max_bar_width = int(w * 0.9)  # 90% of vehicle width
        speed_ratio = min(vehicle['speed'] / 100.0, 1.0)  # Normalize to 0-1
        bar_width = int(max_bar_width * speed_ratio)
        
        # Determine bar color based on speed
        thresholds = self.control_panel.get_thresholds()
        bar_color = self.get_speed_color(vehicle['speed'], thresholds)
        
        # Draw speed bar
        bar_y = y - h//2 - 20
        cv2.rectangle(frame, (x-max_bar_width//2, bar_y), 
                     (x-max_bar_width//2 + bar_width, bar_y + 8), bar_color, -1)
        
        # Draw bar outline
        cv2.rectangle(frame, (x-max_bar_width//2, bar_y), 
                     (x+max_bar_width//2, bar_y + 8), (255, 255, 255), 1)
        
        # Draw speed text
        speed_text = f"{vehicle['speed']:.1f} km/h"
        cv2.putText(frame, speed_text, (x-max_bar_width//2, bar_y-5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    def get_speed_color(self, speed, thresholds):
        """Get color based on speed threshold"""
        if speed < thresholds.get('slow', 30):
            return (52, 152, 219)  # Blue
        elif speed < thresholds.get('normal', 50):
            return (46, 204, 113)  # Green
        elif speed < thresholds.get('fast', 70):
            return (243, 156, 18)  # Orange
        else:
            return (231, 76, 60)   # Red
    
    def update_demo_statistics(self, current_vehicles, speeds):
        """Update statistics for demo mode"""
        thresholds = self.control_panel.get_thresholds()
        
        self.statistics['current_vehicles'] = current_vehicles
        self.statistics['total_vehicles'] = max(self.statistics['total_vehicles'], current_vehicles)
        
        if speeds:
            self.statistics['avg_speed'] = sum(speeds) / len(speeds)
            self.statistics['max_speed'] = max(speeds)
            self.statistics['violations'] = sum(1 for s in speeds if s > thresholds.get('speeding', 80))
        
        # Update display
        self.stats_panel.update_statistics(self.statistics)
        
        # Update speed data for chart
        current_time = time.time() - self.start_time
        self.speed_data.append(self.statistics['avg_speed'])
        self.time_data.append(current_time)
        
        # Keep only last 100 data points
        if len(self.speed_data) > 100:
            self.speed_data = self.speed_data[-100:]
            self.time_data = self.time_data[-100:]
    
    def process_video(self):
        """Process uploaded video file"""
        if not self.cap:
            return
        
        self.is_playing = True
        
        def video_thread():
            while self.is_playing and not self.is_demo_mode:
                ret, frame = self.cap.read()
                if not ret:
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Loop video
                    continue
                
                # Process frame for vehicle detection
                processed_frame = self.process_frame(frame)
                
                # Display frame
                self.video_display.update_frame(processed_frame)
                
                # Update statistics
                self.update_video_statistics()
                
                time.sleep(1/self.fps)  # Maintain video FPS
        
        # Start video processing in separate thread
        video_thread = threading.Thread(target=video_thread, daemon=True)
        video_thread.start()
    
    def process_frame(self, frame):
        """Process a single video frame"""
        # Detect vehicles
        vehicles, fg_mask = self.vehicle_detector.detect_vehicles(frame)
        
        # Track vehicles
        tracks = self.vehicle_detector.track_vehicles(vehicles)
        
        # Draw detections and tracks
        for track_id, track in tracks.items():
            x, y, w, h = track['bbox']
            center = track['center']
            speed = track['speed']
            
            # Classify vehicle
            vehicle_type = self.vehicle_detector.classify_vehicle(track['bbox'])
            
            # Draw bounding box
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # Draw vehicle type and ID
            cv2.putText(frame, f"{vehicle_type.upper()} #{track_id}", 
                       (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # Draw speed
            thresholds = self.control_panel.get_thresholds()
            color = self.vehicle_detector.get_speed_color(speed, thresholds)
            cv2.putText(frame, f"{speed:.1f} km/h", 
                       (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return frame
    
    def update_video_statistics(self):
        """Update statistics for video processing"""
        # This would be implemented based on actual vehicle detection results
        pass
    
    def update_speed_chart(self):
        """Update the speed chart"""
        thresholds = self.control_panel.get_thresholds()
        self.speed_chart.update_chart(self.speed_data, self.time_data, thresholds)
    
    def toggle_play(self):
        """Toggle play/pause"""
        self.is_playing = not self.is_playing
        status = "Playing" if self.is_playing else "Paused"
        self.video_display.update_status(f"Status: {status}")
    
    def stop_analysis(self):
        """Stop video analysis"""
        self.is_playing = False
        self.is_demo_mode = False
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        self.video_display.update_status("Analysis stopped")
    
    def export_statistics(self):
        """Export statistics to CSV"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"traffic_stats_{timestamp}.csv"
        
        filename = filedialog.asksaveasfilename(
            title="Export Statistics",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialvalue=default_filename
        )
        
        if filename:
            try:
                # Prepare data
                data = {
                    'Timestamp': [datetime.now()],
                    'Total_Vehicles': [self.statistics['total_vehicles']],
                    'Current_Vehicles': [self.statistics['current_vehicles']],
                    'Average_Speed': [self.statistics['avg_speed']],
                    'Max_Speed': [self.statistics['max_speed']],
                    'Violations': [self.statistics['violations']],
                    'Detection_Rate': [self.statistics['detection_rate']]
                }
                
                df = pd.DataFrame(data)
                df.to_csv(filename, index=False)
                
                self.show_info("Export Complete", f"Statistics exported to {filename}")
                
            except Exception as e:
                self.show_error("Export Error", f"Failed to export statistics: {str(e)}")
    
    def reset_statistics(self):
        """Reset all statistics"""
        self.statistics = {
            'total_vehicles': 0,
            'current_vehicles': 0,
            'avg_speed': 0,
            'max_speed': 0,
            'violations': 0,
            'detection_rate': 0
        }
        
        self.speed_data = []
        self.time_data = []
        self.start_time = time.time()
        
        self.stats_panel.update_statistics(self.statistics)
        self.speed_chart.update_chart([], [], {})
    
    def show_error(self, title, message):
        """Show error dialog"""
        messagebox.showerror(title, message)
    
    def show_info(self, title, message):
        """Show info dialog"""
        messagebox.showinfo(title, message)
    
    def run(self):
        """Start the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\nApplication terminated by user")
        except Exception as e:
            self.show_error("Application Error", f"Unexpected error: {str(e)}")
        finally:
            self.stop_analysis()

def main():
    """Main entry point"""
    print("Starting Traffic Video Analysis System...")
    print("Loading components...")
    
    try:
        app = TrafficAnalysisApp()
        print("System ready!")
        app.run()
    except Exception as e:
        print(f"Failed to start application: {e}")
        print("Please check that all dependencies are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)

if __name__ == "__main__":
    main() 