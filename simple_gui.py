#!/usr/bin/env python3
"""
Simple GUI version without matplotlib to ensure basic functionality
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import cv2
import numpy as np
import random
from PIL import Image, ImageTk

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vehicle_detector import VehicleDetector
from speed_calculator import SpeedCalculator

class SimpleTrafficApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_gui()
        self.initialize_system()
        
    def setup_window(self):
        """Setup main window"""
        self.root.title("Traffic Video Analysis System - Simple GUI")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
    def setup_gui(self):
        """Setup GUI components"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left side - Video display
        left_frame = ttk.LabelFrame(main_frame, text="Video Display", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Video area
        self.video_label = tk.Label(left_frame, 
                                   bg='#2c3e50', 
                                   fg='white',
                                   text="🚗 Traffic Video Analysis System\n\nClick 'Start Demo' to see simulation\nor 'Upload Video' to analyze a file",
                                   font=('Arial', 14),
                                   width=80, 
                                   height=25,
                                   relief='sunken',
                                   borderwidth=2)
        self.video_label.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Status
        self.status_label = ttk.Label(left_frame, text="System Ready", 
                                     font=('Arial', 10, 'bold'))
        self.status_label.pack(pady=5)
        
        # Control buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="📁 Upload Video", 
                  command=self.upload_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🚗 Start Demo", 
                  command=self.start_demo).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="⏹️ Stop", 
                  command=self.stop).pack(side=tk.LEFT, padx=5)
        
        # Right side - Statistics
        right_frame = ttk.LabelFrame(main_frame, text="Statistics", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # Statistics display
        self.stats_text = tk.Text(right_frame, width=30, height=20, 
                                 font=('Courier', 10))
        self.stats_text.pack(fill=tk.BOTH, expand=True)
        
        # Speed legend
        legend_frame = ttk.LabelFrame(right_frame, text="Speed Legend", padding=5)
        legend_frame.pack(fill=tk.X, pady=10)
        
        legend_items = [
            ("🔵 Slow (< 30 km/h)", "blue"),
            ("🟢 Normal (30-50 km/h)", "green"),
            ("🟠 Fast (50-70 km/h)", "orange"),
            ("🔴 Speeding (> 70 km/h)", "red")
        ]
        
        for text, color in legend_items:
            label = tk.Label(legend_frame, text=text, fg=color, font=('Arial', 9))
            label.pack(anchor=tk.W)
        
    def initialize_system(self):
        """Initialize the system"""
        self.vehicle_detector = VehicleDetector()
        self.speed_calculator = SpeedCalculator()
        self.is_running = False
        self.demo_vehicles = []
        self.statistics = {
            'total_vehicles': 0,
            'current_vehicles': 0,
            'avg_speed': 0,
            'max_speed': 0,
            'violations': 0
        }
        self.update_stats_display()
        
    def upload_video(self):
        """Upload video file"""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv")]
        )
        if file_path:
            self.status_label.config(text=f"Loaded: {os.path.basename(file_path)}")
            messagebox.showinfo("Video Loaded", "Video processing will be implemented in full version")
    
    def start_demo(self):
        """Start demo simulation"""
        if self.is_running:
            return
            
        self.is_running = True
        self.status_label.config(text="Demo Mode Active")
        
        # Create demo vehicles
        self.demo_vehicles = []
        for i in range(4):
            self.demo_vehicles.append({
                'x': random.randint(50, 750),
                'y': 150 + i * 100,
                'speed': random.uniform(25, 75),
                'type': random.choice(['car', 'truck', 'bus', 'motorcycle']),
                'id': 1000 + i
            })
        
        # Start demo thread
        demo_thread = threading.Thread(target=self.run_demo, daemon=True)
        demo_thread.start()
    
    def run_demo(self):
        """Run demo simulation"""
        frame_count = 0
        while self.is_running:
            # Create demo frame
            frame = np.zeros((600, 800, 3), dtype=np.uint8)
            frame[:] = (50, 50, 50)  # Dark background
            
            # Draw road lanes
            for i in range(1, 4):
                y = 150 * i
                cv2.line(frame, (0, y), (800, y), (255, 255, 255), 2)
            
            # Update and draw vehicles
            speeds = []
            for vehicle in self.demo_vehicles:
                # Update position
                vehicle['x'] += vehicle['speed'] * 0.1
                if vehicle['x'] > 850:
                    vehicle['x'] = -50
                
                # Draw vehicle
                x, y = int(vehicle['x']), int(vehicle['y'])
                color = self.get_vehicle_color(vehicle['type'])
                cv2.rectangle(frame, (x-20, y-10), (x+20, y+10), color, -1)
                cv2.rectangle(frame, (x-20, y-10), (x+20, y+10), (255, 255, 255), 2)
                
                # Draw speed
                speed_color = self.get_speed_color(vehicle['speed'])
                cv2.putText(frame, f"{vehicle['speed']:.1f} km/h", 
                           (x-30, y-15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, speed_color, 1)
                
                speeds.append(vehicle['speed'])
            
            # Update statistics
            self.statistics['current_vehicles'] = len(self.demo_vehicles)
            self.statistics['total_vehicles'] = max(self.statistics['total_vehicles'], len(self.demo_vehicles))
            if speeds:
                self.statistics['avg_speed'] = sum(speeds) / len(speeds)
                self.statistics['max_speed'] = max(speeds)
                self.statistics['violations'] = sum(1 for s in speeds if s > 70)
            
            # Update display
            self.update_video_display(frame)
            self.update_stats_display()
            
            frame_count += 1
            time.sleep(0.1)  # 10 FPS
    
    def get_vehicle_color(self, vehicle_type):
        """Get color for vehicle type"""
        colors = {
            'car': (0, 255, 0),
            'truck': (255, 0, 0),
            'bus': (0, 0, 255),
            'motorcycle': (255, 255, 0)
        }
        return colors.get(vehicle_type, (0, 255, 0))
    
    def get_speed_color(self, speed):
        """Get color based on speed"""
        if speed < 30:
            return (255, 0, 0)  # Blue
        elif speed < 50:
            return (0, 255, 0)  # Green
        elif speed < 70:
            return (0, 165, 255)  # Orange
        else:
            return (0, 0, 255)  # Red
    
    def update_video_display(self, frame):
        """Update video display"""
        try:
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(frame_rgb)
            photo = ImageTk.PhotoImage(pil_image)
            
            # Update label
            self.video_label.configure(image=photo, text="")
            self.video_label.image = photo  # Keep reference
        except Exception as e:
            print(f"Display update error: {e}")
    
    def update_stats_display(self):
        """Update statistics display"""
        stats_text = f"""
📊 TRAFFIC STATISTICS

🚗 Total Vehicles: {self.statistics['total_vehicles']}
🚙 Current Vehicles: {self.statistics['current_vehicles']}
📈 Average Speed: {self.statistics['avg_speed']:.1f} km/h
⚡ Maximum Speed: {self.statistics['max_speed']:.1f} km/h
⚠️  Speed Violations: {self.statistics['violations']}

🎯 SYSTEM STATUS
Status: {'Running' if self.is_running else 'Ready'}
Demo Vehicles: {len(self.demo_vehicles)}

💡 INSTRUCTIONS
1. Click 'Start Demo' to see simulation
2. Click 'Upload Video' to analyze files
3. Click 'Stop' to end current operation

🚀 The system is working properly!
        """
        
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
    
    def stop(self):
        """Stop current operation"""
        self.is_running = False
        self.status_label.config(text="Stopped")
        
        # Reset video display
        self.video_label.configure(image="", 
                                  text="🚗 Traffic Video Analysis System\n\nClick 'Start Demo' to see simulation\nor 'Upload Video' to analyze a file")
        
        self.update_stats_display()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🚗 Starting Simple Traffic Analysis GUI...")
    app = SimpleTrafficApp()
    app.run()

if __name__ == "__main__":
    main()
