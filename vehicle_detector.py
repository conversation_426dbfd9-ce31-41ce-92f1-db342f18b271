import cv2
import numpy as np
from collections import deque
import time

class VehicleDetector:
    def __init__(self):
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            history=500, varThreshold=50, detectShadows=True
        )
        self.kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        self.vehicle_tracks = {}
        self.track_id = 0
        self.max_disappeared = 30
        self.min_area = 1000
        self.max_area = 50000
        
    def detect_vehicles(self, frame):
        """Detect vehicles using background subtraction and contour analysis"""
        # Apply background subtraction
        fg_mask = self.background_subtractor.apply(frame)
        
        # Apply morphological operations
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, self.kernel)
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, self.kernel)
        
        # Find contours
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter and process contours
        vehicles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_area < area < self.max_area:
                x, y, w, h = cv2.boundingRect(contour)
                center = (x + w//2, y + h//2)
                
                # Calculate aspect ratio to filter out non-vehicle objects
                aspect_ratio = w / float(h)
                if 0.5 < aspect_ratio < 3.0:  # Vehicle-like aspect ratio
                    vehicles.append({
                        'bbox': (x, y, w, h),
                        'center': center,
                        'area': area,
                        'contour': contour
                    })
        
        return vehicles, fg_mask
    
    def track_vehicles(self, vehicles):
        """Track vehicles across frames using centroid tracking"""
        if len(vehicles) == 0:
            # Mark all existing tracks as disappeared
            for track_id in list(self.vehicle_tracks.keys()):
                self.vehicle_tracks[track_id]['disappeared'] += 1
                
                # Remove tracks that have disappeared for too long
                if self.vehicle_tracks[track_id]['disappeared'] > self.max_disappeared:
                    del self.vehicle_tracks[track_id]
            return []
        
        # If no existing tracks, create new ones
        if len(self.vehicle_tracks) == 0:
            for vehicle in vehicles:
                self.vehicle_tracks[self.track_id] = {
                    'center': vehicle['center'],
                    'bbox': vehicle['bbox'],
                    'disappeared': 0,
                    'speed': 0,
                    'last_time': time.time(),
                    'last_position': vehicle['center']
                }
                self.track_id += 1
        else:
            # Calculate distances between existing tracks and new detections
            track_ids = list(self.vehicle_tracks.keys())
            track_centers = [self.vehicle_tracks[track_id]['center'] for track_id in track_ids]
            
            # Calculate Euclidean distances
            distances = []
            for vehicle in vehicles:
                for track_center in track_centers:
                    distance = np.sqrt(
                        (vehicle['center'][0] - track_center[0])**2 + 
                        (vehicle['center'][1] - track_center[1])**2
                    )
                    distances.append(distance)
            
            # Match detections to tracks
            used_tracks = set()
            used_detections = set()
            
            for i, track_id in enumerate(track_ids):
                if track_id in used_tracks:
                    continue
                    
                min_distance = float('inf')
                min_detection_idx = -1
                
                for j, vehicle in enumerate(vehicles):
                    if j in used_detections:
                        continue
                    
                    distance = distances[i * len(vehicles) + j]
                    if distance < min_distance and distance < 100:  # Max distance threshold
                        min_distance = distance
                        min_detection_idx = j
                
                if min_detection_idx != -1:
                    # Update track
                    vehicle = vehicles[min_detection_idx]
                    self.vehicle_tracks[track_id]['center'] = vehicle['center']
                    self.vehicle_tracks[track_id]['bbox'] = vehicle['bbox']
                    self.vehicle_tracks[track_id]['disappeared'] = 0
                    
                    # Calculate speed
                    current_time = time.time()
                    time_diff = current_time - self.vehicle_tracks[track_id]['last_time']
                    if time_diff > 0:
                        distance = np.sqrt(
                            (vehicle['center'][0] - self.vehicle_tracks[track_id]['last_position'][0])**2 +
                            (vehicle['center'][1] - self.vehicle_tracks[track_id]['last_position'][1])**2
                        )
                        speed = distance / time_diff * 3.6  # Convert to km/h
                        self.vehicle_tracks[track_id]['speed'] = speed
                        self.vehicle_tracks[track_id]['last_time'] = current_time
                        self.vehicle_tracks[track_id]['last_position'] = vehicle['center']
                    
                    used_tracks.add(track_id)
                    used_detections.add(min_detection_idx)
                else:
                    # Mark track as disappeared
                    self.vehicle_tracks[track_id]['disappeared'] += 1
            
            # Remove tracks that have disappeared for too long
            for track_id in list(self.vehicle_tracks.keys()):
                if self.vehicle_tracks[track_id]['disappeared'] > self.max_disappeared:
                    del self.vehicle_tracks[track_id]
            
            # Create new tracks for unmatched detections
            for i, vehicle in enumerate(vehicles):
                if i not in used_detections:
                    self.vehicle_tracks[self.track_id] = {
                        'center': vehicle['center'],
                        'bbox': vehicle['bbox'],
                        'disappeared': 0,
                        'speed': 0,
                        'last_time': time.time(),
                        'last_position': vehicle['center']
                    }
                    self.track_id += 1
        
        return self.vehicle_tracks
    
    def classify_vehicle(self, bbox):
        """Classify vehicle type based on size and aspect ratio"""
        x, y, w, h = bbox
        area = w * h
        aspect_ratio = w / float(h)
        
        if area < 2000:
            return "motorcycle"
        elif area < 5000:
            return "car"
        elif area < 15000:
            return "truck"
        else:
            return "bus"
    
    def get_speed_color(self, speed, thresholds):
        """Get color based on speed threshold"""
        if speed < thresholds.get('slow', 30):
            return (52, 152, 219)  # Blue
        elif speed < thresholds.get('normal', 50):
            return (46, 204, 113)  # Green
        elif speed < thresholds.get('fast', 70):
            return (243, 156, 18)  # Orange
        else:
            return (231, 76, 60)   # Red