#!/usr/bin/env python3
"""
Test the demo functionality without GUI
"""

import sys
import os
import time
import numpy as np
import cv2

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vehicle_detector import VehicleDetector
from speed_calculator import SpeedCalculator

def test_demo_simulation():
    """Test demo simulation without GUI"""
    print("Testing demo simulation...")
    
    try:
        # Initialize components
        detector = VehicleDetector()
        calculator = SpeedCalculator()
        
        # Create demo vehicles
        demo_vehicles = []
        for i in range(3):
            demo_vehicles.append({
                'type': 'car',
                'x': 100 + i * 200,
                'y': 200,
                'width': 60,
                'height': 30,
                'speed': 45.0,
                'direction': 1,
                'lane': 0,
                'id': 1000 + i
            })
        
        print(f"Created {len(demo_vehicles)} demo vehicles")
        
        # Simulate a few frames
        for frame_num in range(5):
            # Create demo frame
            frame = np.zeros((600, 800, 3), dtype=np.uint8)
            frame[:] = (50, 50, 50)  # Dark gray background
            
            # Draw road lanes
            height, width = frame.shape[:2]
            for i in range(1, 4):
                y = height // 4 * i
                cv2.line(frame, (0, y), (width, y), (255, 255, 255), 2)
            
            # Update and draw vehicles
            for vehicle in demo_vehicles:
                # Update position
                vehicle['x'] += vehicle['speed'] * 0.1
                if vehicle['x'] > 850:
                    vehicle['x'] = -50
                
                # Draw vehicle
                x, y = int(vehicle['x']), int(vehicle['y'])
                w, h = vehicle['width'], vehicle['height']
                
                cv2.rectangle(frame, (x-w//2, y-h//2), (x+w//2, y+h//2), (0, 255, 0), -1)
                cv2.rectangle(frame, (x-w//2, y-h//2), (x+w//2, y+h//2), (255, 255, 255), 2)
                
                # Draw speed text
                cv2.putText(frame, f"{vehicle['speed']:.1f} km/h", 
                           (x-w//2, y+h//2+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            print(f"Frame {frame_num + 1}: Processed {len(demo_vehicles)} vehicles")
        
        print("✅ Demo simulation working")
        return True
        
    except Exception as e:
        print(f"❌ Demo simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vehicle_tracking():
    """Test vehicle tracking functionality"""
    print("Testing vehicle tracking...")
    
    try:
        detector = VehicleDetector()
        
        # Create test vehicles at different positions
        vehicles1 = [
            {'center': (100, 200), 'bbox': (80, 180, 40, 40), 'area': 1600},
            {'center': (300, 200), 'bbox': (280, 180, 40, 40), 'area': 1600}
        ]
        
        vehicles2 = [
            {'center': (110, 200), 'bbox': (90, 180, 40, 40), 'area': 1600},
            {'center': (310, 200), 'bbox': (290, 180, 40, 40), 'area': 1600}
        ]
        
        # Track vehicles across frames
        tracks1 = detector.track_vehicles(vehicles1)
        tracks2 = detector.track_vehicles(vehicles2)
        
        print(f"Frame 1: {len(tracks1)} tracks")
        print(f"Frame 2: {len(tracks2)} tracks")
        
        # Test speed color function
        thresholds = {'slow': 30, 'normal': 50, 'fast': 70}
        color = detector.get_speed_color(45, thresholds)
        print(f"Speed color for 45 km/h: {color}")
        
        print("✅ Vehicle tracking working")
        return True
        
    except Exception as e:
        print(f"❌ Vehicle tracking failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_speed_calculation():
    """Test speed calculation"""
    print("Testing speed calculation...")
    
    try:
        calculator = SpeedCalculator()
        
        # Test speed calculation with moving vehicle
        current_time = time.time()
        
        # First position
        speed1 = calculator.calculate_speed(1, (100, 200), current_time)
        print(f"Initial speed: {speed1} km/h")
        
        # Second position (after small delay)
        time.sleep(0.1)
        speed2 = calculator.calculate_speed(1, (110, 200), current_time + 0.1)
        print(f"Speed after movement: {speed2} km/h")
        
        # Test statistics
        stats = calculator.get_speed_statistics(1)
        if stats:
            print(f"Speed statistics: {stats}")
        
        print("✅ Speed calculation working")
        return True
        
    except Exception as e:
        print(f"❌ Speed calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run demo tests"""
    print("🚗 Demo Functionality Test")
    print("=" * 40)
    
    tests = [
        ("Demo Simulation", test_demo_simulation),
        ("Vehicle Tracking", test_vehicle_tracking),
        ("Speed Calculation", test_speed_calculation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
        print()
    
    print("=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All demo tests passed!")
    else:
        print("⚠️  Some demo tests failed.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
